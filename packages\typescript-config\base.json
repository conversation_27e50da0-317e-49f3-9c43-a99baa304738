{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"declaration": true, "declarationMap": true, "esModuleInterop": true, "incremental": false, "isolatedModules": true, "moduleDetection": "force", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2022", "forceConsistentCasingInFileNames": true, "sourceMap": true, "module": "ESNext", "noEmit": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["DOM", "DOM.Iterable", "ESNext"]}}