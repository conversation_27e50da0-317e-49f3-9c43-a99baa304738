{"name": "web", "version": "0.1.0", "type": "module", "private": true, "description": "<PERSON><PERSON><PERSON>", "scripts": {"dev": "vite dev", "build": "vite build", "lint": "eslint . --max-warnings 0 --fix", "check-types": "tsc --noEmit"}, "packageManager": "pnpm@10.12.4", "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tanstack/react-router": "^1.125.4", "@tanstack/react-start": "^1.125.5", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.30.1", "eslint-import-resolver-alias": "^1.1.2", "prettier": "^3.6.2", "typescript": "^5.8.3", "vite": "^7.0.2", "vite-tsconfig-paths": "^5.1.4"}}